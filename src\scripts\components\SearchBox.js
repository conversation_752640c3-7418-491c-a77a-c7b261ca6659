/**
 * 搜索框组件
 * 负责处理用户搜索输入和搜索事件
 */
export class SearchBox {
    constructor(onSearch) {
        this.onSearch = onSearch;
        this.init();
    }

    init() {
        this.bindEvents();
    }

    bindEvents() {
        // 搜索按钮点击事件
        const searchBtn = document.getElementById('searchBtn');
        if (searchBtn) {
            searchBtn.addEventListener('click', () => this.handleSearch());
        }

        // 搜索框回车事件
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('keypress', e => {
                if (e.key === 'Enter') {
                    this.handleSearch();
                }
            });
        }
    }

    handleSearch() {
        const keyword = this.getKeyword();
        if (keyword && this.onSearch) {
            this.onSearch(keyword);
        }
    }

    getKeyword() {
        const searchInput = document.getElementById('searchInput');
        return searchInput ? searchInput.value.trim() : '';
    }

    setKeyword(keyword) {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.value = keyword;
        }
    }

    focus() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.focus();
        }
    }

    clear() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.value = '';
        }
    }
}
