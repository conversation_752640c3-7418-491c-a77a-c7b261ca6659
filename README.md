# 网盘搜索应用

基于 Cloudflare Workers 的网盘搜索页面，提供美观的界面和强大的搜索功能。

## 功能特性

- 🔍 **智能搜索**: 支持关键词搜索，调用多个网盘搜索源
- 📱 **响应式设计**: 适配桌面和移动设备
- 🎨 **美观界面**: 紫色渐变背景，卡片式布局
- 🏷️ **动态分类**: 根据搜索结果自动生成网盘类型筛选标签，显示每类结果数量
- ⏰ **时间排序**: 支持按时间排序（最新优先/最旧优先）
- 📄 **分页功能**: 支持大量搜索结果的分页展示
- ⚡ **快速部署**: 基于 Cloudflare Workers，全球 CDN 加速

## 支持的网盘类型

- 天翼网盘
- 百度网盘
- 阿里云盘
- 夸克网盘
- 蓝奏云
- OneDrive
- Google Drive
- 其他主流网盘（动态识别）

## 部署说明

### 前置要求

1. 安装 Node.js (版本 16 或更高)
2. 注册 Cloudflare 账户
3. 安装 Wrangler CLI

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd pansou-search
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **登录 Cloudflare**
   ```bash
   npx wrangler login
   ```

4. **本地开发**
   ```bash
   npm run dev
   ```

5. **部署到生产环境**
   ```bash
   npm run deploy:production
   ```

### 配置说明

- `wrangler.toml`: Cloudflare Workers 配置文件
- `worker.js`: 主要的 Workers 脚本，包含所有功能
- 所有静态资源（HTML、CSS、JavaScript）都内联在 worker.js 中

## 使用说明

1. 访问部署后的网址
2. 在搜索框中输入关键词
3. 点击搜索按钮或按回车键
4. 搜索完成后，系统会自动生成动态筛选标签，显示各网盘类型的结果数量
5. 使用筛选按钮按网盘类型过滤结果
6. 使用排序下拉菜单按时间排序（默认排序/最新优先/最旧优先）
7. 点击"打开链接"访问资源
8. 如有提取码会显示在结果卡片中

## API 接口

应用调用的搜索 API：
- **URL**: `https://pansou.252035.xyz/api/search`
- **参数**:
  - `kw`: 搜索关键词（必需）
  - `refresh`: 是否刷新缓存（默认 false）
  - `res`: 结果类型（默认 "merge"）
  - `src`: 搜索源（默认 "all"）
  - `plugins`: 插件列表

## 技术栈

- **后端**: Cloudflare Workers
- **前端**: 原生 HTML/CSS/JavaScript
- **样式**: CSS Grid + Flexbox
- **部署**: Wrangler CLI

## 自定义配置

### 修改样式
编辑 `worker.js` 中的 CSS 部分来自定义界面样式。

### 修改搜索参数
在 `handleSearchAPI` 函数中修改默认的搜索参数。

### 添加新的网盘类型
在 `getPlatformName` 函数中添加新的网盘类型映射。

## 注意事项

1. 确保 Cloudflare Workers 的使用限制满足需求
2. 搜索 API 可能有频率限制，请合理使用
3. 部署前请测试所有功能是否正常

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。
