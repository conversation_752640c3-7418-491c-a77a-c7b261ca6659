import { NetworkError, APIError, TimeoutError, globalErrorHandler } from './error-handler.js';

/**
 * API 工具函数
 * 提供统一的 API 请求接口和错误处理
 */

/**
 * 发送 API 请求
 * @param {string} url - 请求URL
 * @param {Object} params - 请求参数
 * @param {Object} options - 请求选项
 * @returns {Promise<Object>} API响应数据
 */
export async function apiRequest(url, params = {}, options = {}) {
    try {
        const requestUrl = buildUrl(url, params);
        const requestOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            },
            ...options
        };

        const response = await fetch(requestUrl, requestOptions);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error('API request failed:', error);
        throw new APIError(error.message, error);
    }
}

/**
 * 构建带参数的URL
 * @param {string} baseUrl - 基础URL
 * @param {Object} params - 参数对象
 * @returns {string} 完整的URL
 */
function buildUrl(baseUrl, params) {
    if (!params || Object.keys(params).length === 0) {
        return baseUrl;
    }

    const url = new URL(baseUrl, window.location.origin);
    Object.entries(params).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
            url.searchParams.set(key, value);
        }
    });

    return url.toString();
}

/**
 * 搜索API请求
 * @param {string} keyword - 搜索关键词
 * @param {Object} options - 搜索选项
 * @returns {Promise<Object>} 搜索结果
 */
export async function searchFiles(keyword, options = {}) {
    const params = {
        kw: keyword,
        refresh: options.refresh || 'false',
        res: options.res || 'merge',
        src: options.src || 'all',
        plugins: options.plugins || 'pansearch,qupansou,panta,pan666,hunhepan,jikepan'
    };

    return apiRequest('/api/search', params);
}

/**
 * API 错误类
 */
export class APIError extends Error {
    constructor(message, originalError = null) {
        super(message);
        this.name = 'APIError';
        this.originalError = originalError;
    }
}

/**
 * 请求重试工具
 * @param {Function} requestFn - 请求函数
 * @param {number} maxRetries - 最大重试次数
 * @param {number} delay - 重试延迟（毫秒）
 * @returns {Promise<any>} 请求结果
 */
export async function retryRequest(requestFn, maxRetries = 3, delay = 1000) {
    let lastError;

    for (let i = 0; i <= maxRetries; i++) {
        try {
            return await requestFn();
        } catch (error) {
            lastError = error;

            if (i === maxRetries) {
                break;
            }

            // 等待指定时间后重试
            await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
        }
    }

    throw lastError;
}

/**
 * 请求缓存管理
 */
export class RequestCache {
    constructor(maxSize = 100, ttl = 5 * 60 * 1000) {
        this.cache = new Map();
        this.maxSize = maxSize;
        this.ttl = ttl;
    }

    /**
     * 获取缓存数据
     * @param {string} key - 缓存键
     * @returns {any|null} 缓存数据或null
     */
    get(key) {
        const item = this.cache.get(key);
        if (!item) {
            return null;
        }

        if (Date.now() - item.timestamp > this.ttl) {
            this.cache.delete(key);
            return null;
        }

        return item.data;
    }

    /**
     * 设置缓存数据
     * @param {string} key - 缓存键
     * @param {any} data - 缓存数据
     */
    set(key, data) {
        if (this.cache.size >= this.maxSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }

        this.cache.set(key, {
            data,
            timestamp: Date.now()
        });
    }

    /**
     * 清除缓存
     */
    clear() {
        this.cache.clear();
    }

    /**
     * 删除指定缓存
     * @param {string} key - 缓存键
     */
    delete(key) {
        this.cache.delete(key);
    }
}

// 创建全局请求缓存实例
export const requestCache = new RequestCache();

/**
 * 带缓存的API请求
 * @param {string} url - 请求URL
 * @param {Object} params - 请求参数
 * @param {Object} options - 请求选项
 * @returns {Promise<Object>} API响应数据
 */
export async function cachedApiRequest(url, params = {}, options = {}) {
    const cacheKey = `${url}?${new URLSearchParams(params).toString()}`;

    // 尝试从缓存获取
    const cachedData = requestCache.get(cacheKey);
    if (cachedData && !options.skipCache) {
        return cachedData;
    }

    // 发送请求并缓存结果
    const data = await apiRequest(url, params, options);
    requestCache.set(cacheKey, data);

    return data;
}
