import { ERROR_MESSAGES } from '../../config/constants.js';

/**
 * 错误类型枚举
 */
export const ErrorTypes = {
    NETWORK_ERROR: 'NETWORK_ERROR',
    VALIDATION_ERROR: 'VALIDATION_ERROR',
    API_ERROR: 'API_ERROR',
    TIMEOUT_ERROR: 'TIMEOUT_ERROR',
    UNKNOWN_ERROR: 'UNKNOWN_ERROR'
};

/**
 * 自定义错误类
 */
export class AppError extends Error {
    constructor(message, type = ErrorTypes.UNKNOWN_ERROR, originalError = null) {
        super(message);
        this.name = 'AppError';
        this.type = type;
        this.originalError = originalError;
        this.timestamp = new Date().toISOString();
    }
}

/**
 * 网络错误类
 */
export class NetworkError extends AppError {
    constructor(message, originalError = null) {
        super(message, ErrorTypes.NETWORK_ERROR, originalError);
        this.name = 'NetworkError';
    }
}

/**
 * 验证错误类
 */
export class ValidationError extends AppError {
    constructor(message, field = null, originalError = null) {
        super(message, ErrorTypes.VALIDATION_ERROR, originalError);
        this.name = 'ValidationError';
        this.field = field;
    }
}

/**
 * API 错误类
 */
export class APIError extends AppError {
    constructor(message, status = 500, originalError = null) {
        super(message, ErrorTypes.API_ERROR, originalError);
        this.name = 'APIError';
        this.status = status;
    }
}

/**
 * 超时错误类
 */
export class TimeoutError extends AppError {
    constructor(message, originalError = null) {
        super(message, ErrorTypes.TIMEOUT_ERROR, originalError);
        this.name = 'TimeoutError';
    }
}

/**
 * 错误处理器类
 */
export class ErrorHandler {
    constructor() {
        this.errorListeners = [];
        this.retryAttempts = new Map();
    }

    /**
     * 添加错误监听器
     * @param {Function} listener - 错误监听器函数
     */
    addErrorListener(listener) {
        this.errorListeners.push(listener);
    }

    /**
     * 移除错误监听器
     * @param {Function} listener - 错误监听器函数
     */
    removeErrorListener(listener) {
        const index = this.errorListeners.indexOf(listener);
        if (index > -1) {
            this.errorListeners.splice(index, 1);
        }
    }

    /**
     * 处理错误
     * @param {Error} error - 错误对象
     * @param {Object} context - 错误上下文
     * @returns {Object} 处理后的错误信息
     */
    handleError(error, context = {}) {
        const processedError = this.processError(error, context);
        
        // 通知所有错误监听器
        this.errorListeners.forEach(listener => {
            try {
                listener(processedError);
            } catch (listenerError) {
                console.error('Error in error listener:', listenerError);
            }
        });

        return processedError;
    }

    /**
     * 处理错误对象
     * @param {Error} error - 错误对象
     * @param {Object} context - 错误上下文
     * @returns {Object} 处理后的错误信息
     */
    processError(error, context) {
        let errorType = ErrorTypes.UNKNOWN_ERROR;
        let userMessage = ERROR_MESSAGES.UNKNOWN_ERROR;
        let shouldRetry = false;

        if (error instanceof AppError) {
            errorType = error.type;
            userMessage = error.message;
        } else if (error.name === 'TypeError' && error.message.includes('fetch')) {
            errorType = ErrorTypes.NETWORK_ERROR;
            userMessage = ERROR_MESSAGES.NETWORK_ERROR;
            shouldRetry = true;
        } else if (error.name === 'AbortError' || error.name === 'TimeoutError') {
            errorType = ErrorTypes.TIMEOUT_ERROR;
            userMessage = ERROR_MESSAGES.TIMEOUT_ERROR;
            shouldRetry = true;
        } else if (error.message.includes('HTTP')) {
            errorType = ErrorTypes.API_ERROR;
            userMessage = ERROR_MESSAGES.SERVER_ERROR;
            shouldRetry = true;
        }

        return {
            type: errorType,
            message: userMessage,
            originalError: error,
            context,
            shouldRetry,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 重试操作
     * @param {Function} operation - 要重试的操作
     * @param {string} operationId - 操作标识
     * @param {number} maxRetries - 最大重试次数
     * @param {number} delay - 重试延迟
     * @returns {Promise} 操作结果
     */
    async retry(operation, operationId, maxRetries = 3, delay = 1000) {
        const currentAttempts = this.retryAttempts.get(operationId) || 0;
        
        try {
            const result = await operation();
            this.retryAttempts.delete(operationId);
            return result;
        } catch (error) {
            if (currentAttempts >= maxRetries) {
                this.retryAttempts.delete(operationId);
                throw error;
            }

            this.retryAttempts.set(operationId, currentAttempts + 1);
            
            // 等待指定时间后重试
            await new Promise(resolve => setTimeout(resolve, delay * (currentAttempts + 1)));
            
            return this.retry(operation, operationId, maxRetries, delay);
        }
    }

    /**
     * 清除重试记录
     * @param {string} operationId - 操作标识
     */
    clearRetryAttempts(operationId) {
        this.retryAttempts.delete(operationId);
    }

    /**
     * 获取用户友好的错误消息
     * @param {Error} error - 错误对象
     * @returns {string} 用户友好的错误消息
     */
    getUserFriendlyMessage(error) {
        const processedError = this.processError(error);
        return processedError.message;
    }
}

/**
 * 全局错误处理器实例
 */
export const globalErrorHandler = new ErrorHandler();

/**
 * 输入验证工具
 */
export class InputValidator {
    /**
     * 验证搜索关键词
     * @param {string} keyword - 搜索关键词
     * @throws {ValidationError} 验证失败时抛出错误
     */
    static validateSearchKeyword(keyword) {
        if (!keyword || typeof keyword !== 'string') {
            throw new ValidationError(ERROR_MESSAGES.INVALID_KEYWORD, 'keyword');
        }

        const trimmedKeyword = keyword.trim();
        
        if (trimmedKeyword.length === 0) {
            throw new ValidationError(ERROR_MESSAGES.INVALID_KEYWORD, 'keyword');
        }

        if (trimmedKeyword.length < 1) {
            throw new ValidationError(ERROR_MESSAGES.KEYWORD_TOO_SHORT, 'keyword');
        }

        if (trimmedKeyword.length > 100) {
            throw new ValidationError(ERROR_MESSAGES.KEYWORD_TOO_LONG, 'keyword');
        }

        return trimmedKeyword;
    }

    /**
     * 验证页码
     * @param {number} page - 页码
     * @param {number} maxPage - 最大页码
     * @throws {ValidationError} 验证失败时抛出错误
     */
    static validatePage(page, maxPage = Number.MAX_SAFE_INTEGER) {
        if (!Number.isInteger(page) || page < 1) {
            throw new ValidationError('页码必须是大于0的整数', 'page');
        }

        if (page > maxPage) {
            throw new ValidationError(`页码不能超过${maxPage}`, 'page');
        }

        return page;
    }

    /**
     * 验证每页大小
     * @param {number} pageSize - 每页大小
     * @throws {ValidationError} 验证失败时抛出错误
     */
    static validatePageSize(pageSize) {
        if (!Number.isInteger(pageSize) || pageSize < 1 || pageSize > 100) {
            throw new ValidationError('每页大小必须是1-100之间的整数', 'pageSize');
        }

        return pageSize;
    }

    /**
     * 清理和验证HTML内容
     * @param {string} html - HTML内容
     * @returns {string} 清理后的HTML内容
     */
    static sanitizeHTML(html) {
        if (!html || typeof html !== 'string') {
            return '';
        }

        // 简单的HTML清理，移除潜在危险的标签和属性
        return html
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
            .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
            .replace(/on\w+="[^"]*"/gi, '')
            .replace(/javascript:/gi, '');
    }
}
