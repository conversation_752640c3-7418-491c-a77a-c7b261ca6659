import htmlTemplate from './index.html';
import mainCSS from '../styles/main.css';
import componentsCSS from '../styles/components.css';
import responsiveCSS from '../styles/responsive.css';

/**
 * 生成完整的 HTML 页面
 * 将所有 CSS 和 JavaScript 内联到 HTML 中
 * @returns {string} 完整的 HTML 字符串
 */
export async function generateHTML() {
    try {
        // 合并所有 CSS
        const combinedCSS = [mainCSS, componentsCSS, responsiveCSS].join('\n');

        // 生成 JavaScript 代码
        const jsCode = generateJavaScript();

        // 替换 HTML 模板中的占位符
        let html = htmlTemplate;

        // 注入 CSS
        html = html.replace('/* Placeholder for CSS content */', combinedCSS);

        // 注入 JavaScript
        html = html.replace('/* Placeholder for JavaScript content */', jsCode);

        return html;
    } catch (error) {
        console.error('HTML generation error:', error);
        throw error;
    }
}

/**
 * 生成 JavaScript 代码
 * @returns {string} JavaScript 代码字符串
 */
function generateJavaScript() {
    // 这里我们需要将模块化的 JavaScript 代码转换为可以在浏览器中运行的代码
    // 由于 Cloudflare Workers 的限制，我们需要将所有代码内联

    return `
        // 网盘搜索应用 JavaScript 代码
        (function() {
            'use strict';
            
            // 工具函数
            function getPlatformName(platform) {
                const names = {
                    'tianyi': '天翼网盘',
                    'baidu': '百度网盘',
                    'aliyun': '阿里云盘',
                    'quark': '夸克网盘',
                    'lanzou': '蓝奏云',
                    'onedrive': 'OneDrive',
                    'googledrive': 'Google Drive'
                };
                return names[platform] || platform;
            }

            // API 请求函数
            async function apiRequest(url, params = {}) {
                const requestUrl = new URL(url, window.location.origin);
                Object.entries(params).forEach(([key, value]) => {
                    if (value !== null && value !== undefined) {
                        requestUrl.searchParams.set(key, value);
                    }
                });

                const response = await fetch(requestUrl.toString());
                if (!response.ok) {
                    throw new Error(\`HTTP error! status: \${response.status}\`);
                }
                return response.json();
            }

            // 主应用类
            class PanSearchApp {
                constructor() {
                    this.currentResults = [];
                    this.currentFilter = 'all';
                    this.currentSort = 'default';
                    this.currentPage = 1;
                    this.pageSize = 12;
                    this.availablePlatforms = new Map();
                    this.init();
                }

                init() {
                    this.bindEvents();
                }

                bindEvents() {
                    // 搜索按钮点击
                    const searchBtn = document.getElementById('searchBtn');
                    if (searchBtn) {
                        searchBtn.addEventListener('click', () => this.search());
                    }

                    // 搜索框回车
                    const searchInput = document.getElementById('searchInput');
                    if (searchInput) {
                        searchInput.addEventListener('keypress', (e) => {
                            if (e.key === 'Enter') this.search();
                        });
                    }

                    // 排序选择
                    const sortSelect = document.getElementById('sortSelect');
                    if (sortSelect) {
                        sortSelect.addEventListener('change', (e) => {
                            this.currentSort = e.target.value;
                            this.currentPage = 1;
                            this.renderResults();
                        });
                    }

                    // 视图切换
                    document.querySelectorAll('.view-btn').forEach(btn => {
                        btn.addEventListener('click', (e) => {
                            document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                            e.target.classList.add('active');
                        });
                    });
                }

                async search() {
                    const keyword = document.getElementById('searchInput').value.trim();
                    if (!keyword) return;

                    this.showLoading();

                    try {
                        const data = await apiRequest('/api/search', { kw: keyword });

                        if (data.code === 0) {
                            this.currentResults = this.processResults(data.data);
                            this.generateDynamicFilters();
                            this.currentPage = 1;
                            this.currentFilter = 'all';
                            this.currentSort = 'default';
                            document.getElementById('sortSelect').value = 'default';
                            this.renderResults();
                        } else {
                            this.showError('搜索失败：' + (data.message || '未知错误'));
                        }
                    } catch (error) {
                        this.showError('网络错误，请稍后重试');
                    }
                }

                processResults(data) {
                    const results = [];
                    this.availablePlatforms.clear();

                    if (data.merged_by_type) {
                        Object.entries(data.merged_by_type).forEach(([platform, items]) => {
                            this.availablePlatforms.set(platform, {
                                name: getPlatformName(platform),
                                count: items.length
                            });

                            items.forEach(item => {
                                results.push({
                                    ...item,
                                    platform: platform,
                                    platformName: getPlatformName(platform)
                                });
                            });
                        });
                    }

                    return results;
                }

                generateDynamicFilters() {
                    const filtersContainer = document.getElementById('dynamicFilters');
                    if (!filtersContainer) return;

                    const allFilesBtn = filtersContainer.querySelector('[data-type="all"]');
                    filtersContainer.innerHTML = '';
                    
                    if (allFilesBtn) {
                        filtersContainer.appendChild(allFilesBtn);
                    } else {
                        this.createAllFilesButton(filtersContainer);
                    }

                    const totalCount = this.currentResults.length;
                    const allBtn = filtersContainer.querySelector('[data-type="all"]');
                    if (allBtn) {
                        allBtn.innerHTML = \`所有文件 <span class="count">(\${totalCount})</span>\`;
                    }

                    this.availablePlatforms.forEach((info, platform) => {
                        this.createFilterButton(filtersContainer, platform, info);
                    });

                    this.setActiveFilter('all');
                }

                createAllFilesButton(container) {
                    const btn = document.createElement('button');
                    btn.className = 'filter-btn active';
                    btn.dataset.type = 'all';
                    btn.innerHTML = '所有文件';
                    
                    btn.addEventListener('click', (e) => {
                        this.handleFilterClick(e);
                    });

                    container.appendChild(btn);
                }

                createFilterButton(container, platform, info) {
                    const btn = document.createElement('button');
                    btn.className = 'filter-btn';
                    btn.dataset.type = platform;
                    btn.innerHTML = \`\${info.name} <span class="count">(\${info.count})</span>\`;

                    btn.addEventListener('click', (e) => {
                        this.handleFilterClick(e);
                    });

                    container.appendChild(btn);
                }

                handleFilterClick(e) {
                    const filterType = e.target.dataset.type;
                    if (filterType) {
                        this.setActiveFilter(filterType);
                        this.currentFilter = filterType;
                        this.currentPage = 1;
                        this.renderResults();
                    }
                }

                setActiveFilter(filterType) {
                    const container = document.getElementById('dynamicFilters');
                    if (!container) return;

                    const buttons = container.querySelectorAll('.filter-btn');
                    buttons.forEach(btn => btn.classList.remove('active'));

                    const activeBtn = container.querySelector(\`[data-type="\${filterType}"]\`);
                    if (activeBtn) {
                        activeBtn.classList.add('active');
                    }
                }

                getFilteredResults() {
                    let results;
                    if (this.currentFilter === 'all') {
                        results = [...this.currentResults];
                    } else {
                        results = this.currentResults.filter(item => item.platform === this.currentFilter);
                    }

                    return this.sortResults(results);
                }

                sortResults(results) {
                    if (this.currentSort === 'default') {
                        return results;
                    }

                    return results.sort((a, b) => {
                        const dateA = a.datetime ? new Date(a.datetime) : new Date(0);
                        const dateB = b.datetime ? new Date(b.datetime) : new Date(0);

                        if (this.currentSort === 'newest') {
                            return dateB - dateA;
                        } else if (this.currentSort === 'oldest') {
                            return dateA - dateB;
                        }

                        return 0;
                    });
                }

                renderResults() {
                    const filteredResults = this.getFilteredResults();
                    const startIndex = (this.currentPage - 1) * this.pageSize;
                    const endIndex = startIndex + this.pageSize;
                    const pageResults = filteredResults.slice(startIndex, endIndex);

                    this.hideAll();
                    document.getElementById('resultsSection').style.display = 'block';

                    document.getElementById('resultsCount').textContent = \`找到 \${filteredResults.length} 个文件\`;

                    const grid = document.getElementById('resultsGrid');
                    grid.innerHTML = pageResults.map(item => this.createResultCard(item)).join('');

                    this.renderPagination(filteredResults.length);
                }

                createResultCard(item) {
                    const icon = this.getFileIcon(item.note);
                    const platformClass = \`platform-\${item.platform}\`;
                    const date = item.datetime ? new Date(item.datetime).toLocaleDateString() : '未知';

                    return \`
                        <div class="result-card">
                            <div class="card-header">
                                <div class="file-icon" style="background: \${icon.color};">\${icon.icon}</div>
                                <div class="file-title">\${item.note || '未命名文件'}</div>
                            </div>
                            <div class="file-meta">
                                <span>\${date}</span>
                                <span class="platform-badge \${platformClass}">\${item.platformName}</span>
                            </div>
                            <div class="file-actions">
                                <a href="\${item.url}" target="_blank" class="action-btn">打开链接</a>
                                \${item.password ? \`<span class="action-btn">密码: \${item.password}</span>\` : ''}
                            </div>
                        </div>
                    \`;
                }

                getFileIcon(filename) {
                    if (!filename) return { icon: '📄', color: '#6c757d' };

                    const ext = filename.split('.').pop()?.toLowerCase();
                    const icons = {
                        'pdf': { icon: '📄', color: '#dc3545' },
                        'doc': { icon: '📝', color: '#0d6efd' },
                        'docx': { icon: '📝', color: '#0d6efd' },
                        'xls': { icon: '📊', color: '#198754' },
                        'xlsx': { icon: '📊', color: '#198754' },
                        'ppt': { icon: '📊', color: '#fd7e14' },
                        'pptx': { icon: '📊', color: '#fd7e14' },
                        'zip': { icon: '🗜️', color: '#6f42c1' },
                        'rar': { icon: '🗜️', color: '#6f42c1' },
                        'mp4': { icon: '🎬', color: '#e83e8c' },
                        'avi': { icon: '🎬', color: '#e83e8c' },
                        'mp3': { icon: '🎵', color: '#20c997' },
                        'jpg': { icon: '🖼️', color: '#fd7e14' },
                        'png': { icon: '🖼️', color: '#fd7e14' },
                        'txt': { icon: '📄', color: '#6c757d' }
                    };

                    return icons[ext] || { icon: '📄', color: '#6c757d' };
                }

                renderPagination(totalItems) {
                    const totalPages = Math.ceil(totalItems / this.pageSize);
                    const pagination = document.getElementById('pagination');

                    if (totalPages <= 1) {
                        pagination.innerHTML = '';
                        return;
                    }

                    let html = '';

                    html += \`<button class="page-btn" \${this.currentPage === 1 ? 'disabled' : ''} onclick="app.goToPage(\${this.currentPage - 1})">上一页</button>\`;

                    for (let i = Math.max(1, this.currentPage - 2); i <= Math.min(totalPages, this.currentPage + 2); i++) {
                        html += \`<button class="page-btn \${i === this.currentPage ? 'active' : ''}" onclick="app.goToPage(\${i})">\${i}</button>\`;
                    }

                    html += \`<button class="page-btn" \${this.currentPage === totalPages ? 'disabled' : ''} onclick="app.goToPage(\${this.currentPage + 1})">下一页</button>\`;

                    pagination.innerHTML = html;
                }

                goToPage(page) {
                    this.currentPage = page;
                    this.renderResults();
                }

                showLoading() {
                    this.hideAll();
                    document.getElementById('loading').style.display = 'block';
                }

                showError(message) {
                    this.hideAll();
                    const errorEl = document.getElementById('error');
                    errorEl.textContent = message;
                    errorEl.style.display = 'block';
                }

                hideAll() {
                    document.getElementById('resultsSection').style.display = 'none';
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('error').style.display = 'none';
                    document.getElementById('empty').style.display = 'none';
                }
            }

            // 初始化应用
            window.app = new PanSearchApp();
        })();
    `;
}
